

import asyncdispatch, asynchttpserver, ws, json
import utils/log
import dispatcher

type
  BotServer* = ref object
    server: AsyncHttpServer
    port: int
    dispatcher: Dispatcher

proc newBotServer*(port: int = 33894): BotServer =
  BotServer(
    server: newAsyncHttpServer(), 
    port: port,
    dispatcher: dispatcher.newDispatcher()
  )

proc handleMessage(server: BotServer, ws: WebSocket, msg: string) {.async, gcsafe.} =
  logInfo("收到消息: " & msg)
  try:
    let event = parseJson(msg)
    await server.dispatcher.handle(event, ws)
  except:
    logError("消息处理失败: " & getCurrentExceptionMsg())

proc onConnect(server: BotServer, ws: WebSocket) {.async, gcsafe.} =
  logInfo("客户端已连接")
  try:
    while ws.readyState == Open:
      let msg = await ws.receiveStrPacket()
      await server.handleMessage(ws, msg)
  except WebSocketError as e:
    logError("WebSocket错误: " & e.msg)
  except Exception as e:
    logError("连接异常: " & e.msg)
  finally:
    logInfo("客户端连接已断开")

proc start*(bot: BotServer) {.async.} =
  proc cb(req: Request) {.async, gcsafe.} =
    if req.url.path == "/":
      try:
        let ws = await newWebSocket(req)
        await bot.onConnect(ws)
      except WebSocketError as e:
        logError("WebSocket握手失败: " & e.msg)
        await req.respond(Http400, "WebSocket握手失败")
    else:
      await req.respond(Http404, "Not Found")
  
  try:
    logInfo("服务器启动成功，端口: " & $bot.port)
    await bot.server.serve(Port(bot.port), cb)
  except Exception as e:
    logError("服务器启动失败: " & e.msg)
