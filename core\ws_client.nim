

import asyncdispatch, ws
import utils/log

type
  BotClient* = ref object
    ws: WebSocket
    url: string

proc newBotClient*(url: string): BotClient =
  BotClient(url: url)

proc sendMessage*(client: BotClient, msg: string) {.async.} =
  if client.ws != nil and client.ws.readyState == Open:
    await client.ws.send(msg)
    logInfo("发送消息: " & msg)

proc connect*(client: BotClient) {.async.} =
  client.ws = await newWebSocket(client.url)
  logInfo("已连接到: " & client.url)
